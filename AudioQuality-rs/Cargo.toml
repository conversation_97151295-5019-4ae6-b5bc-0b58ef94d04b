[package]
name = "AudioQuality-rs"
version = "4.0.0"
edition = "2021"
authors = ["Audio Analyzer Team"]
description = "Pure Rust Audio Quality Analyzer for macOS ARM64"

[dependencies]
# 核心依赖
chrono = { version = "0.4", features = ["serde"] }
rayon = "1.8"
regex = "1.10"
serde = { version = "1.0", features = ["derive"] }
walkdir = "2.4"
lazy_static = "1.4"
anyhow = "1.0"
tempfile = "3.10.1"
csv = "1.3" # 新增：用于生成CSV报告
which = "4.4" # 新增：用于在系统 PATH 中查找 ffmpeg

# 命令行和UI
indicatif = "0.17"
clap = { version = "4.5.11", features = ["derive"] }

# 系统相关
serde_json = "1.0.142"

# macOS ARM64 优化
[profile.release]
codegen-units = 1
lto = "fat"
panic = "abort"
opt-level = 3



[profile.release-performance]
inherits = "release"
lto = "fat"          # 最大化链接时优化
codegen-units = 1    # 单一代码生成单元以最大化优化机会
opt-level = 3        # 最高优化级别

