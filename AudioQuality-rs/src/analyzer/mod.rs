// ----------------------------------------------------------------
// 项目: 音频质量分析器 (Audio Quality Analyzer)
// 模块: analyzer/mod.rs
// 描述: 这是 `analyzer` 模块的根文件 (module root)。
//      它的主要作用是声明该模块下的所有子模块，使得这些子模块
//      可以被项目的其他部分（如此处的 `main.rs`）所访问。
//      通过这种方式，我们将相关的分析功能组织在一起，
//      提高了代码的模块化程度和可维护性。
// ----------------------------------------------------------------

/// 声明 `ffmpeg` 子模块。
/// `pub` 关键字使其成为公共模块，意味着在 `analyzer` 模块外部可以访问
/// `analyzer::ffmpeg`。该模块封装了所有与 FFmpeg 命令行工具的交互逻辑。
pub mod ffmpeg;

/// 声明 `metrics` 子模块。
/// `pub` 关键字使其成为公共模块。该模块定义了项目中用于表示
/// 音频技术指标的核心数据结构，例如 `FileMetrics`。
pub mod metrics;