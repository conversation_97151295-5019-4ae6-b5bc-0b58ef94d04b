// ----------------------------------------------------------------
// 项目: 音频质量分析器 (Audio Quality Analyzer)
// 模块: main.rs
// 描述: 本文件是程序的入口点。它支持两种操作模式：
//      1. 直接模式：通过命令行参数接收一个路径，立即执行分析。
//      2. 交互模式：当没有提供命令行参数时，启动一个菜单驱动的界面，
//         允许用户选择不同的操作，如开始分析或退出程序。
// ----------------------------------------------------------------

mod analyzer;

// --- 核心依赖导入 ---
use crate::analyzer::{
    ffmpeg,
    metrics::FileMetrics,
    scoring::QualityScorer,
    report::ReportGenerator,
};
use anyhow::{anyhow, Context, Result};
use chrono::Local;
use clap::Parser;
use indicatif::{ProgressBar, ProgressStyle};
use rayon::prelude::*;
use std::env;
use std::fs;
use std::io::{self, Write};
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use which::which; // 导入 which crate

const SUPPORTED_EXTENSIONS: [&str; 10] = [
    "wav", "mp3", "m4a", "flac", "aac", "ogg", "opus", "wma", "aiff", "alac",
];

/// 定义程序的命令行接口 (CLI)。
#[derive(Parser, Debug)]
#[command(
    author,
    version,
    about = "一个基于 FFmpeg 的纯 Rust 音频质量分析工具",
    long_about = "该工具可以递归扫描指定目录下的音频文件，并行提取多项技术指标，并生成一份详细的 JSON 格式报告。如果未提供路径参数，则会进入交互模式。"
)]
struct Cli {
    /// (可选) 指定要递归扫描和处理的音乐顶层文件夹路径。
    /// 如果不提供，程序将进入交互模式。
    #[arg(value_name = "PATH")]
    path: Option<PathBuf>,
}

// --- 交互模式函数 ---

/// 显示交互式主菜单。
fn show_menu() {
    println!("\n--- 交互模式菜单 ---");
    println!("1. 分析音频文件");
    println!("2. 退出");
    print!("请选择一个操作: ");
    io::stdout().flush().unwrap();
}

/// 启动并管理交互式会话的主循环。
fn interactive_mode() -> Result<()> {
    loop {
        show_menu();

        let mut choice = String::new();
        io::stdin().read_line(&mut choice)?;

        match choice.trim() {
            "1" => {
                // 用户选择开始分析，提示输入路径
                match get_path_from_user_interaction() {
                    Ok(path) => {
                        // 成功获取路径后，执行分析
                        if let Err(e) = run_analysis(&path) {
                            eprintln!("\n分析过程中发生错误: {}", e);
                        }
                    }
                    Err(e) => {
                        // 获取路径失败
                        eprintln!("\n无法获取有效路径: {}", e);
                    }
                }
            }
            "2" => {
                // 用户选择退出
                println!("感谢使用，程序退出。");
                break;
            }
            _ => {
                // 无效输入
                eprintln!("无效的选择，请输入 '1' 或 '2'。");
            }
        }
    }
    Ok(())
}

/// 通过交互式命令行提示，从用户那里获取并验证一个有效的文件夹路径。
fn get_path_from_user_interaction() -> Result<PathBuf> {
    loop {
        print!("请输入要递归处理的音乐顶层文件夹路径: ");
        io::stdout().flush()?;

        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let path = PathBuf::from(input.trim());

        if path.is_dir() {
            return Ok(path.canonicalize()?);
        } else {
            eprintln!(
                "错误: \"{}\" 不是一个有效的文件夹路径或不存在，请重新输入。",
                path.display()
            );
        }
    }
}

// --- 核心分析逻辑 ---

/// 查找 FFmpeg 可执行文件。
///
/// 查找顺序:
/// 1. 首先在系统的 PATH 环境变量中查找 `ffmpeg`。
/// 2. 如果找不到，则回退到在项目 `resources` 目录中查找。
/// 3. 如果都找不到，返回一个错误。
fn find_ffmpeg_path() -> Result<PathBuf> {
    // 优先：在系统 PATH 中查找
    match which("ffmpeg") {
        Ok(path) => {
            println!("成功在系统 PATH 中找到 FFmpeg: {}", path.display());
            Ok(path)
        }
        Err(_) => {
            // 次选：在本地 resources 目录中查找
            println!("未在系统 PATH 中找到 FFmpeg，正在尝试备用路径...");
            let current_exe_path = env::current_exe()?;
            let project_root = current_exe_path
                .ancestors()
                .nth(3)
                .unwrap_or_else(|| Path::new(""));
            let ffmpeg_path = project_root.join("resources/ffmpeg");

            if ffmpeg_path.exists() {
                println!("成功在 resources 目录中找到 FFmpeg: {}", ffmpeg_path.display());
                Ok(ffmpeg_path)
            } else {
                Err(anyhow!(
                    "错误: 在系统 PATH 和备用目录 ({}) 中都找不到 ffmpeg 可执行文件。\n请采取以下任一措施后重试:\n1. 安装 FFmpeg 并确保其位于您的系统 PATH 中。\n2. 将 FFmpeg 可执行文件放置在上述备用目录中。",
                    ffmpeg_path.display()
                ))
            }
        }
    }
}


/// 对指定路径下的音频文件执行完整的分析流程。
///
/// # 参数
/// - `base_folder_path`: 要扫描和分析的根目录的路径。
fn run_analysis(base_folder_path: &Path) -> Result<()> {
    println!("\n--- ✨ 开始执行分析流程 ---");
    println!("分析开始时间: {}", Local::now().format("%Y-%m-%d %H:%M:%S"));

    // --- 环境检查：定位 FFmpeg ---
    let ffmpeg_path = find_ffmpeg_path()?;
    
    println!("正在扫描文件夹: {}", base_folder_path.display());

    // --- 文件扫描 ---
    let audio_files: Vec<PathBuf> = WalkDir::new(base_folder_path)
        .into_iter()
        .filter_map(Result::ok)
        .filter(|e| e.file_type().is_file())
        .map(|e| e.into_path())
        .filter(|path| {
            path.extension()
                .and_then(|s| s.to_str())
                .map(|ext| SUPPORTED_EXTENSIONS.contains(&ext.to_lowercase().as_str()))
                .unwrap_or(false)
        })
        .collect();

    if audio_files.is_empty() {
        println!("在指定路径下没有找到支持的音频文件。");
        return Ok(())
    }

    let total_files = audio_files.len();
    println!(
        "扫描完成，找到 {} 个音频文件待处理。开始并行分析...",
        total_files
    );

    // --- 并行处理 ---
    let bar = ProgressBar::new(total_files as u64);
    bar.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({percent}%) - {msg}")
            .unwrap()
            .progress_chars("#>- "),
    );

    let results: Vec<FileMetrics> = audio_files
        .into_par_iter()
        .map(|path| {
            bar.set_message(
                path.file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .into_owned(),
            );
            let result = ffmpeg::process_file(&path, &ffmpeg_path);
            bar.inc(1);
            result
        })
        .filter_map(|res| match res {
            Ok(m) => Some(m),
            Err(e) => {
                bar.println(format!("处理失败: {}", e));
                None
            }
        })
        .collect();

    bar.finish_with_message("数据提取完成。");

    // --- 质量评分分析 ---
    println!("正在进行质量评分分析...");
    let scorer = QualityScorer::new();
    let quality_analyses = scorer.analyze_files(&results);

    // --- 生成报告 ---
    let report_generator = ReportGenerator::new();

    // 保存CSV报告
    let csv_output_path = base_folder_path.join("audio_quality_report.csv");
    report_generator.generate_csv_report(&quality_analyses, &csv_output_path)?;

    // 显示分析摘要
    report_generator.display_summary(&quality_analyses);

    // --- 保存原始JSON数据 ---
    let json_output_path = base_folder_path.join("analysis_data.json");
    println!("\n正在保存原始数据到: {}", json_output_path.display());

    fs::write(
        &json_output_path,
        serde_json::to_string_pretty(&results)?,
    )
    .context("无法写入 analysis_data.json 文件")?;
    println!("原始数据保存成功！");

    // --- 任务结束 ---
    println!("\n分析结束时间: {}", Local::now().format("%Y-%m-%d %H:%M:%S"));
    println!("--- ✅ 分析流程顺利完成 ---");

    Ok(())
}

// --- 程序入口 ---

/// 程序的主函数，根据命令行参数决定进入直接模式还是交互模式。
fn main() -> Result<()> {
    let cli = Cli::parse();

    println!("欢迎使用音频质量分析器 (Rust 重构版)");

    match cli.path {
        // 模式一：用户通过命令行参数提供了路径
        Some(path) => {
            if path.is_dir() {
                let absolute_path = path.canonicalize()?;
                run_analysis(&absolute_path)
            } else {
                Err(anyhow!(
                    "错误: 命令行提供的路径 \"{}\" 不是一个有效的文件夹或不存在。",
                    path.display()
                ))
            }
        }
        // 模式二：没有提供命令行参数，进入交互模式
        None => interactive_mode(),
    }
}

// --- 单元测试 ---
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_supported_extensions_are_lowercase() {
        for &ext in SUPPORTED_EXTENSIONS.iter() {
            assert_eq!(
                ext,
                ext.to_lowercase(),
                "支持的扩展名 '{}' 应该全部为小写。",
                ext
            );
        }
    }
}