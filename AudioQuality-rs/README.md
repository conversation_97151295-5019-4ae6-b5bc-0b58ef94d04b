# 音频质量分析器 (AudioQuality-rs)

[![语言 (Language)](https://img.shields.io/badge/language-Rust-orange.svg)](https://www.rust-lang.org/)
[![许可证 (License)](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

**中文 (Chinese) | [English](#english-version)**

这是一个使用纯 Rust 编写的高性能音频质量分析工具。它通过并行调用 FFmpeg，能够高效处理大量音频文件，提取关键技术指标，并生成一份中间 JSON 报告用于后续的质量评估。

该项目专注于性能和可靠性，通过 Rust 的并行处理能力 (Rayon) 和健壮的错误处理机制，为音频分析提供了一个坚实的数据提取基础。

---

## ✨ 主要特性 (Features)

- **高性能并行处理 (High-Performance Parallel Processing)**: 利用 [Rayon](https://github.com/rayon-rs/rayon) 库，能够并发执行多个 FFmpeg 进程，极大地缩短了分析大型音乐库所需的时间。
- **全面的技术指标提取 (Comprehensive Metric Extraction)**: 提取一系列关键的音频技术指标，包括：
    - **响度范围 (Loudness Range, LRA)**: 基于 EBU R128 标准。
    - **峰值与 RMS (Peak & RMS)**: 整体峰值电平和均方根电平。
    - **高频段能量 (High-Frequency Energy)**: 16kHz, 18kHz, 20kHz 以上频段的 RMS 电平，是判断“假无损” (transcodes) 的重要依据。
- **命令行驱动 (Command-Line Driven)**: 使用 [Clap](https://github.com/clap-rs/clap) 构建了清晰的命令行接口，易于使用和集成到自动化脚本中。
- **自包含依赖 (Self-Contained Dependency)**: 依赖一个放置在 `resources` 目录下的 `ffmpeg` 可执行文件，无需用户在系统范围内安装。
- **结构化输出 (Structured Output)**: 生成一份 `analysis_data.json` 文件，其中包含每个音轨的原始技术指标，格式清晰，便于其他程序或脚本进行消费和分析。

## 🚀 使用方法 (Usage)

1.  **确保依赖存在**:
    -   在项目的根目录下，必须有一个 `resources` 文件夹。
    -   `ffmpeg` 可执行文件必须位于 `resources/ffmpeg`。

2.  **运行程序**:
    -   通过 `cargo run` 运行，并使用 `--` 将参数传递给程序。
    -   程序需要一个强制性参数：要分析的音频文件夹的路径。

    ```bash
    # 在 Release 模式下运行，以获得最佳性能
    cargo run --release -- /path/to/your/music/folder
    ```

3.  **查看结果**:
    -   分析完成后，一个名为 `analysis_data.json` 的文件将被创建在您指定的音乐文件夹内。

## 🛠️ 从源码编译 (Building from Source)

如果您想从源码自行编译，请确保您已安装 [Rust 工具链](https://www.rust-lang.org/tools/install)。

```bash
# 克隆仓库
git clone <repository-url>
cd AudioQuality-rs

# 以 Release 模式编译优化后的可执行文件
cargo build --release

# 编译后的文件位于 ./target/release/AudioQuality-rs
# 你可以像这样直接运行它：
./target/release/AudioQuality-rs /path/to/your/music/folder
```

## 📂 项目结构 (Project Structure)

```
AudioQuality-rs/
├── .cargo/                # Cargo 配置文件
├── docs/                  # 项目文档
│   ├── SCORING_LOGIC.md   # (外部) 评分算法的逻辑说明
│   └── test_data_description.md # 测试数据的描述
├── resources/             # 存放外部依赖和资源
│   ├── ffmpeg             # 【必需】FFmpeg 可执行文件
│   └── test_data/         # 用于功能测试的音频文件
├── src/                   # 所有 Rust 源代码
│   ├── analyzer/          # 核心分析逻辑模块
│   │   ├── ffmpeg.rs      # 封装所有与 FFmpeg 的交互逻辑
│   │   ├── metrics.rs     # 定义核心数据结构 (如 FileMetrics)
│   │   └── mod.rs         # 声明 analyzer 的子模块
│   └── main.rs            # 程序主入口，负责 CLI 和任务调度
├── .gitignore             # 指定 Git 应忽略的文件
├── Cargo.toml             # 项目元数据和依赖项配置文件
└── README.md              # 您正在阅读的这份文件
```

---

## English Version

This is a high-performance audio quality analysis tool written in pure Rust. By invoking FFmpeg in parallel, it can efficiently process a large number of audio files, extract key technical metrics, and generate an intermediate JSON report for subsequent quality assessment.

The project focuses on performance and reliability, providing a solid data extraction foundation for audio analysis through Rust's parallel processing capabilities (Rayon) and robust error handling.

### ✨ Features

- **High-Performance Parallel Processing**: Leverages the Rayon library to execute multiple FFmpeg processes concurrently, significantly reducing the time required to analyze large music libraries.
- **Comprehensive Metric Extraction**: Extracts a range of key technical audio metrics, including:
    - **Loudness Range (LRA)**: Based on the EBU R128 standard.
    - **Peak & RMS**: Overall peak and root mean square levels.
    - **High-Frequency Energy**: RMS levels above 16kHz, 18kHz, and 20kHz, crucial for detecting transcodes ("fake lossless").
- **Command-Line Driven**: Built with Clap for a clean command-line interface, easy to use and integrate into scripts.
- **Self-Contained Dependency**: Relies on an `ffmpeg` executable placed in the `resources` directory, requiring no system-wide installation from the user.
- **Structured Output**: Generates an `analysis_data.json` file containing the raw technical metrics for each track, clearly formatted for consumption by other programs or scripts.

### 🚀 Usage

1.  **Ensure Dependency**: An `ffmpeg` executable must be present at `resources/ffmpeg`.
2.  **Run**: Use `cargo run --release -- <PATH_TO_FOLDER>`.
3.  **Output**: An `analysis_data.json` file will be created in the target folder.

For build instructions and project structure, please refer to the Chinese version above.