# GitHub搜索查询配置文件
# 每行一个查询语句，支持GitHub搜索语法
# 以#开头的行为注释，空行会被忽略

AIzaSy in:file
AIzaSy filename:.env
AIzaSy filename:.env.local
AIzaSy filename:.env.development
AIzaSy filename:.env.example
AIzaSy filename:config extension:json
AIzaSy filename:config extension:yml
AIzaSy filename:config extension:yaml

API_KEY="AIzaSy" in:file
GEMINI_KEY="AIzaSy" in:file
GEMINI_API_KEY="AIzaSy" in:file

ANTHROPIC_API_KEY in:file
ANTHROPIC_API_KEY filename:.env
ANTHROPIC_API_KEY filename:.env.local
ANTHROPIC_API_KEY filename:.env.development
ANTHROPIC_API_KEY filename:.env.example
ANTHROPIC_API_KEY filename:config extension:json
ANTHROPIC_API_KEY filename:config extension:yml
ANTHROPIC_API_KEY filename:config extension:yaml

sk-ant- in:file
sk-ant- filename:.env
sk-ant- filename:.env.local
sk-ant- filename:.env.development
sk-ant- filename:.env.example
sk-ant- filename:config extension:json
sk-ant- filename:config extension:yml
sk-ant- filename:config extension:yaml

