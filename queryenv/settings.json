{"leetcode.endpoint": "leetcode-cn", "leetcode.workspaceFolder": "/Users/<USER>/.leetcode", "markdown.preview.breaks": true, "leetcode.hint.commentDescription": false, "leetcode.hint.commandShortcut": false, "leetcode.hint.configWebviewMarkdown": false, "explorer.confirmDelete": false, "update.showReleaseNotes": false, "lldb.suppressUpdateNotifications": true, "redhat.telemetry.enabled": false, "leetcode.defaultLanguage": "rust", "workbench.colorTheme": "One Dark Pro", "chat.commandCenter.enabled": false, "cmake.pinnedCommands": ["workbench.action.tasks.configureTaskRunner", "workbench.action.tasks.runTask"]}